'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { reportsApi, cantieriApi } from '@/lib/api'
import { ReportAvanzamento, ReportBOQ, Cantiere } from '@/types'
import {
  BarChart3,
  Download,
  Calendar,
  TrendingUp,
  Target,
  Activity,
  Clock,
  CheckCircle,
  Loader2,
  AlertCircle,
  FileText,
  Package,
  Users,
  Zap,
  RefreshCw
} from 'lucide-react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts'

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState('avanzamento')
  const [selectedPeriod, setSelectedPeriod] = useState('month')
  const [reportAvanzamento, setReportAvanzamento] = useState<any>(null)
  const [reportBOQ, setReportBOQ] = useState<any>(null)
  const [reportUtilizzoBobine, setReportUtilizzoBobine] = useState<any>(null)
  const [reportProgress, setReportProgress] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  const { user, cantiere } = useAuth()



  // Load all basic reports on component mount - copiato dalla webapp originale
  useEffect(() => {
    const loadAllReports = async () => {
      setIsLoading(true)
      try {
        // Usa il cantiere dal contesto di autenticazione
        const currentCantiereId = cantiere?.id_cantiere

        if (!currentCantiereId) {
          setError('Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i report.')
          setIsLoading(false)
          return
        }

        // Create individual promises that handle their own errors - come nella webapp originale
        const progressPromise = reportsApi.getReportProgress(currentCantiereId)
          .then(res => res.data)
          .catch(err => {
            console.error('Error loading progress report:', err)
            return { content: null }
          })

        const boqPromise = reportsApi.getReportBOQ(currentCantiereId)
          .then(res => res.data)
          .catch(err => {
            console.error('Error loading BOQ report:', err)
            return { content: null }
          })

        const utilizzoPromise = reportsApi.getReportUtilizzoBobine(currentCantiereId)
          .then(res => res.data)
          .catch(err => {
            console.error('Error loading utilizzo bobine report:', err)
            return { content: null }
          })

        // Wait for all promises to resolve (they won't reject due to the catch handlers)
        const [progressData, boqData, utilizzoData] = await Promise.all([
          progressPromise,
          boqPromise,
          utilizzoPromise
        ])

        // Set the data for each report, even if some are null
        setReportProgress(progressData?.content || progressData)
        setReportBOQ(boqData?.content || boqData)
        setReportUtilizzoBobine(utilizzoData?.content || utilizzoData)

        // Only set error to null if we successfully loaded at least one report
        if (progressData?.content || boqData?.content || utilizzoData?.content ||
            progressData || boqData || utilizzoData) {
          setError('')
        } else {
          setError('Errore nel caricamento dei report. Riprova più tardi.')
        }
      } catch (err) {
        // This catch block should rarely be hit due to the individual error handling above
        console.error('Unexpected error loading reports:', err)
        setError('Errore nel caricamento dei report. Riprova più tardi.')
      } finally {
        setIsLoading(false)
      }
    }

    if (cantiere?.id_cantiere) {
      loadAllReports()
    } else {
      setIsLoading(false)
      setError('Nessun cantiere selezionato. Seleziona un cantiere per visualizzare i report.')
    }
  }, [cantiere?.id_cantiere])



  const handleRefresh = () => {
    // Ricarica i report per il cantiere corrente
    if (cantiere?.id_cantiere) {
      setIsLoading(true)
      setError('')
      // Trigger useEffect by updating a dependency
      window.location.reload()
    }
  }

  const handleExportReport = async (reportType: string, format: string = 'pdf') => {
    try {
      const currentCantiereId = cantiere?.id_cantiere
      if (!currentCantiereId) {
        console.error('Nessun cantiere selezionato per export')
        return
      }

      let response
      switch (reportType) {
        case 'progress':
          response = await reportsApi.getReportProgress(currentCantiereId)
          break
        case 'boq':
          response = await reportsApi.getReportBOQ(currentCantiereId)
          break
        case 'utilizzo-bobine':
          response = await reportsApi.getReportUtilizzoBobine(currentCantiereId)
          break
        default:
          return
      }

      if (response.data.file_url) {
        // Apri il file in una nuova finestra
        window.open(response.data.file_url, '_blank')
      }
    } catch (error) {
      console.error('Errore export report:', error)
    }
  }

  // Calcolo IAP (Indice di Avanzamento Ponderato)
  const calculateIAP = (nTot: number, nInst: number, nColl: number, nCert: number): number => {
    const Wp = 2.0  // Peso fase Posa
    const Wc = 1.5  // Peso fase Collegamento
    const Wz = 0.5  // Peso fase Certificazione

    if (nTot === 0) return 0

    const sforzoSoloInstallati = (nInst - nColl) * Wp
    const sforzoSoloCollegati = (nColl - nCert) * (Wp + Wc)
    const sforzoCertificati = nCert * (Wp + Wc + Wz)
    const numeratore = sforzoSoloInstallati + sforzoSoloCollegati + sforzoCertificati

    const denominatore = nTot * (Wp + Wc + Wz)
    return Math.round((numeratore / denominatore) * 10000) / 100
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
      <div className="max-w-[90%] mx-auto space-y-6">

        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-slate-900">Reports</h1>
            <p className="text-slate-600 mt-1">
              {cantiere?.commessa ? `Cantiere: ${cantiere.commessa}` : 'Seleziona un cantiere per visualizzare i report'}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Aggiorna
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span>Caricamento report...</span>
            </div>
          </div>
        ) : error ? (
          <div className="p-6 border border-amber-200 rounded-lg bg-amber-50">
            <div className="flex items-center mb-4">
              <AlertCircle className="h-5 w-5 text-amber-600 mr-2" />
              <span className="text-amber-800 font-medium">
                {error.includes('Nessun cantiere selezionato') || error.includes('Cantiere non selezionato') ? 'Cantiere non selezionato' : 'Errore caricamento report'}
              </span>
            </div>
            <p className="text-amber-700 mb-4">{error}</p>
            <div className="flex gap-2">
              <Button
                onClick={() => window.location.href = '/cantieri'}
                className="bg-amber-600 hover:bg-amber-700 text-white"
              >
                Gestisci Cantieri
              </Button>
              <Button
                variant="outline"
                onClick={handleRefresh}
                className="border-amber-600 text-amber-700 hover:bg-amber-100"
              >
                Riprova
              </Button>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="avanzamento" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Avanzamento
              </TabsTrigger>
              <TabsTrigger value="boq" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                BOQ
              </TabsTrigger>
              <TabsTrigger value="bobine" className="flex items-center gap-2">
                <Package className="h-4 w-4" />
                Bobine
              </TabsTrigger>
              <TabsTrigger value="produttivita" className="flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Produttività
              </TabsTrigger>
            </TabsList>

            {/* Tab Content: Avanzamento */}
            <TabsContent value="avanzamento" className="space-y-6">
              {reportProgress?.content ? (
                <>
                  {/* KPI Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <Card className="border-l-4 border-l-blue-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Metri Totali</CardTitle>
                        <Target className="h-4 w-4 text-blue-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.metri_totali?.toLocaleString() || 0}m
                        </div>
                        <p className="text-xs text-slate-500 mt-2">
                          {reportProgress.content.totale_cavi || 0} cavi totali
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-green-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Metri Posati</CardTitle>
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.metri_posati?.toLocaleString() || 0}m
                        </div>
                        <Progress
                          value={reportProgress.content.percentuale_avanzamento || 0}
                          className="mt-2"
                        />
                        <p className="text-xs text-slate-500 mt-2">
                          {reportProgress.content.percentuale_avanzamento?.toFixed(1) || 0}% completato
                        </p>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-purple-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Media Giornaliera</CardTitle>
                        <TrendingUp className="h-4 w-4 text-purple-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.media_giornaliera?.toFixed(1) || 0}m
                        </div>
                        <p className="text-xs text-slate-500">metri/giorno</p>
                        <div className="flex items-center mt-2">
                          <Activity className="h-3 w-3 text-purple-500 mr-1" />
                          <span className="text-xs text-purple-600">
                            {reportProgress.content.giorni_lavorativi_effettivi || 0} giorni attivi
                          </span>
                        </div>
                      </CardContent>
                    </Card>

                    <Card className="border-l-4 border-l-orange-500">
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Completamento</CardTitle>
                        <Clock className="h-4 w-4 text-orange-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportProgress.content.data_completamento || 'N/A'}
                        </div>
                        <p className="text-xs text-slate-500">stima completamento</p>
                        <div className="flex items-center mt-2">
                          <Calendar className="h-3 w-3 text-orange-500 mr-1" />
                          <span className="text-xs text-orange-600">
                            {reportProgress.content.giorni_stimati || 0} giorni rimanenti
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Charts */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Posa Recente */}
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between">
                        <div>
                          <CardTitle>Posa Recente</CardTitle>
                          <CardDescription>Ultimi 10 giorni di attività</CardDescription>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleExportReport('progress', 'pdf')}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          PDF
                        </Button>
                      </CardHeader>
                      <CardContent>
                        <ResponsiveContainer width="100%" height={300}>
                          <BarChart data={reportProgress.content.posa_recente || []}>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="data" />
                            <YAxis />
                            <Tooltip />
                            <Bar dataKey="metri" fill="#3b82f6" name="Metri Posati" />
                          </BarChart>
                        </ResponsiveContainer>
                      </CardContent>
                    </Card>

                    {/* Statistiche Cavi */}
                    <Card>
                      <CardHeader>
                        <CardTitle>Stato Cavi</CardTitle>
                        <CardDescription>Distribuzione per stato di avanzamento</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-4">
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Cavi Posati</span>
                            <Badge variant="secondary">
                              {reportProgress.content.cavi_posati || 0}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Cavi Rimanenti</span>
                            <Badge variant="outline">
                              {reportProgress.content.cavi_rimanenti || 0}
                            </Badge>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-sm font-medium">Percentuale Cavi</span>
                            <Badge variant="default">
                              {reportProgress.content.percentuale_cavi?.toFixed(1) || 0}%
                            </Badge>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Target className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato di avanzamento disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: BOQ */}
            <TabsContent value="boq" className="space-y-6">
              {reportBOQ?.content ? (
                <>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-semibold">Bill of Quantities</h3>
                      <p className="text-sm text-slate-600">Distinta materiali e fabbisogni</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport('boq', 'excel')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Excel
                    </Button>
                  </div>

                  {/* Cavi per Tipo */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Cavi per Tipologia</CardTitle>
                      <CardDescription>Fabbisogno cavi raggruppati per tipologia e formazione</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-left p-2">Formazione</th>
                              <th className="text-right p-2">Numero Cavi</th>
                              <th className="text-right p-2">Metri Teorici</th>
                              <th className="text-right p-2">Metri Reali</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportBOQ.content.cavi_per_tipo?.map((item: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{item.tipologia}</td>
                                <td className="p-2">{item.formazione}</td>
                                <td className="p-2 text-right">{item.num_cavi}</td>
                                <td className="p-2 text-right">{item.metri_teorici?.toLocaleString()}</td>
                                <td className="p-2 text-right">{item.metri_reali?.toLocaleString()}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Bobine per Tipo */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Bobine Disponibili</CardTitle>
                      <CardDescription>Inventario bobine per tipologia</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-left p-2">Formazione</th>
                              <th className="text-right p-2">Numero Bobine</th>
                              <th className="text-right p-2">Metri Disponibili</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportBOQ.content.bobine_per_tipo?.map((item: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{item.tipologia}</td>
                                <td className="p-2">{item.formazione}</td>
                                <td className="p-2 text-right">{item.num_bobine}</td>
                                <td className="p-2 text-right">
                                  <span className={item.metri_disponibili > 0 ? 'text-green-600' : 'text-red-600'}>
                                    {item.metri_disponibili?.toLocaleString()}
                                  </span>
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato BOQ disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Bobine */}
            <TabsContent value="bobine" className="space-y-6">
              {reportUtilizzoBobine?.content ? (
                <>
                  <div className="flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-semibold">Utilizzo Bobine</h3>
                      <p className="text-sm text-slate-600">Stato e utilizzo delle bobine</p>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleExportReport('utilizzo-bobine', 'excel')}
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Excel
                    </Button>
                  </div>

                  {/* Statistiche Bobine */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Totale Bobine</CardTitle>
                        <Package className="h-4 w-4 text-blue-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.totale_bobine || 0}
                        </div>
                        <p className="text-xs text-slate-500">bobine nel cantiere</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Bobine Attive</CardTitle>
                        <Activity className="h-4 w-4 text-green-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.bobine?.filter((b: any) =>
                            b.stato === 'In uso' || b.stato === 'Disponibile'
                          ).length || 0}
                        </div>
                        <p className="text-xs text-slate-500">disponibili/in uso</p>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium text-slate-600">Utilizzo Medio</CardTitle>
                        <TrendingUp className="h-4 w-4 text-purple-500" />
                      </CardHeader>
                      <CardContent>
                        <div className="text-2xl font-bold text-slate-900">
                          {reportUtilizzoBobine.content.bobine?.length > 0 ?
                            (reportUtilizzoBobine.content.bobine.reduce((acc: number, b: any) =>
                              acc + (b.percentuale_utilizzo || 0), 0) / reportUtilizzoBobine.content.bobine.length
                            ).toFixed(1) : 0}%
                        </div>
                        <p className="text-xs text-slate-500">utilizzo medio</p>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Lista Bobine */}
                  <Card>
                    <CardHeader>
                      <CardTitle>Dettaglio Bobine</CardTitle>
                      <CardDescription>Stato dettagliato di tutte le bobine</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="overflow-x-auto">
                        <table className="w-full text-sm">
                          <thead>
                            <tr className="border-b">
                              <th className="text-left p-2">Codice</th>
                              <th className="text-left p-2">Tipologia</th>
                              <th className="text-right p-2">Metri Totali</th>
                              <th className="text-right p-2">Metri Utilizzati</th>
                              <th className="text-right p-2">Metri Residui</th>
                              <th className="text-right p-2">Utilizzo %</th>
                              <th className="text-left p-2">Stato</th>
                              <th className="text-right p-2">Cavi</th>
                            </tr>
                          </thead>
                          <tbody>
                            {reportUtilizzoBobine.content.bobine?.map((bobina: any, index: number) => (
                              <tr key={index} className="border-b hover:bg-slate-50">
                                <td className="p-2 font-medium">{bobina.codice}</td>
                                <td className="p-2">{bobina.tipologia}</td>
                                <td className="p-2 text-right">{bobina.metri_totali?.toLocaleString()}</td>
                                <td className="p-2 text-right">{bobina.metri_utilizzati?.toLocaleString()}</td>
                                <td className="p-2 text-right">{bobina.metri_residui?.toLocaleString()}</td>
                                <td className="p-2 text-right">
                                  <div className="flex items-center gap-2">
                                    <span>{bobina.percentuale_utilizzo?.toFixed(1)}%</span>
                                    <Progress
                                      value={Math.min(bobina.percentuale_utilizzo || 0, 100)}
                                      className="w-16 h-2"
                                    />
                                  </div>
                                </td>
                                <td className="p-2">
                                  <Badge
                                    variant={
                                      bobina.stato === 'Disponibile' ? 'default' :
                                      bobina.stato === 'In uso' ? 'secondary' :
                                      bobina.stato === 'Terminata' ? 'outline' :
                                      bobina.stato === 'Over' ? 'destructive' : 'outline'
                                    }
                                  >
                                    {bobina.stato}
                                  </Badge>
                                </td>
                                <td className="p-2 text-right">{bobina.totale_cavi_associati || 0}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Package className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                  <p>Nessun dato bobine disponibile</p>
                </div>
              )}
            </TabsContent>

            {/* Tab Content: Produttività */}
            <TabsContent value="produttivita" className="space-y-6">
              <div className="text-center py-12 text-slate-500">
                <Zap className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                <h3 className="text-lg font-semibold mb-2">Produttività</h3>
                <p>Funzionalità in fase di sviluppo</p>
                <p className="text-sm mt-2">
                  Includerà calcoli IAP, statistiche team e analisi performance
                </p>
              </div>
            </TabsContent>

          </Tabs>
        )}

      </div>
    </div>
  )
}
