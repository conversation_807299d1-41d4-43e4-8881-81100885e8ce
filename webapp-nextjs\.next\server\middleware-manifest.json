{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "6f95afe8db756bf8855d56738e366258", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "3300c0b8c04b52c9fbd341a70df48331971f61a7cc3b5d3de4ccd1a8e2760999", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "40c757fee744b800d70f5181a0c09f61bfc4bbb398431ddb02675c8ae6af6539"}}}, "sortedMiddleware": ["/"], "functions": {}}